'use client';

import { motion } from 'motion/react';
import React, { ReactNode, useRef } from 'react';
import Link from 'next/link';
import {
  cleanupFramerMotionElement,
  preventTransformConflicts,
} from '@/utils/animationOptimization';
import {
  useScrollReveal,
  useTextReveal,
  useCardStack,
  useParallaxImproved,
  useScrollOverlay,
  useStaggeredReveal,
  useIndependentScrollTrigger,
  useHeroParallax,
} from '@/hooks/useScrollAnimations';

interface ScrollRevealProps {
  children: ReactNode;
  className?: string;
  threshold?: number;
  delay?: number;
}

export function ScrollReveal({
  children,
  className = '',
  threshold = 0.1,
  delay = 0,
}: ScrollRevealProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { opacity, y, scale } = useScrollReveal(ref, threshold);

  return (
    <motion.div
      ref={ref}
      style={{ opacity, y, scale }}
      transition={{ delay }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

interface TextRevealProps {
  children: ReactNode;
  className?: string;
  delay?: number;
  stagger?: boolean;
}

export function TextReveal({
  children,
  className = '',
  delay = 0,
  stagger = false,
}: TextRevealProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { opacity, y } = useTextReveal(ref, delay);

  if (stagger && typeof children === 'string') {
    const words = children.split(' ');
    return (
      <div ref={ref} className={className}>
        {words.map((word, index) => (
          <motion.span
            key={index}
            style={{
              opacity,
              y,
              display: 'inline-block',
              marginRight: '0.25em',
            }}
            transition={{ delay: delay + index * 0.1 }}
          >
            {word}
          </motion.span>
        ))}
      </div>
    );
  }

  return (
    <motion.div ref={ref} style={{ opacity, y }} className={className}>
      {children}
    </motion.div>
  );
}

interface CardStackProps {
  children: ReactNode;
  index: number;
  total: number;
  className?: string;
}

export function CardStack({
  children,
  index,
  total,
  className = '',
}: CardStackProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { y, scale, opacity, zIndex } = useCardStack(index, total, ref);

  return (
    <motion.div
      ref={ref}
      style={{
        y,
        scale,
        opacity,
        zIndex,
        position: 'relative',
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
}

interface ParallaxEnhancedProps {
  children: ReactNode;
  speed?: number;
  className?: string;
}

export function ParallaxEnhanced({
  children,
  speed = 0.5,
  className = '',
}: ParallaxEnhancedProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { y, scale } = useParallaxImproved(speed, ref);

  return (
    <motion.div ref={ref} style={{ y, scale }} className={className}>
      {children}
    </motion.div>
  );
}

interface ScrollOverlayProps {
  className?: string;
}

export function ScrollOverlay({ className = '' }: ScrollOverlayProps) {
  const ref = useRef<HTMLDivElement>(null);
  const opacity = useScrollOverlay(ref);

  return (
    <motion.div
      ref={ref}
      style={{ opacity }}
      className={`absolute inset-0 bg-background-cream ${className}`}
    />
  );
}

interface AnimatedButtonProps {
  children: ReactNode;
  className?: string;
  onClick?: () => void;
  href?: string;
  variant?: 'primary' | 'secondary' | 'cta';
  glowColor?: string;
  enableGlow?: boolean;
}

export function AnimatedButton({
  children,
  className = '',
  onClick,
  href,
  variant = 'primary',
  glowColor = 'rgba(0, 0, 0, 0.2)',
  enableGlow = false,
}: AnimatedButtonProps) {
  const baseClasses = {
    primary: 'btn-primary',
    secondary: 'btn-secondary',
    cta: 'btn-cta',
  };

  // Check if children contains multiple elements (like text + icon)
  const hasMultipleElements = React.Children.count(children) > 1;
  const flexClasses = hasMultipleElements ? 'flex items-center gap-2' : '';

  // Check if href is internal (starts with /) or external
  const isInternalLink = href && href.startsWith('/');
  const isExternalLink = href && !href.startsWith('/');

  // For internal links, use Next.js Link with motion.div
  if (isInternalLink) {
    const hoverAnimation = enableGlow
      ? {
          scale: 1.02,
          boxShadow: `0 0 20px ${glowColor}`,
          transition: { duration: 0.3 },
        }
      : {
          scale: 1.02,
          transition: { duration: 0.3 },
        };

    return (
      <Link href={href} className={`${baseClasses[variant]} ${className}`}>
        <motion.div
          className={flexClasses}
          whileHover={hoverAnimation}
          whileTap={{
            scale: 0.98,
            transition: { duration: 0.15 },
          }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          style={{
            willChange: enableGlow ? 'transform, box-shadow' : 'transform',
          }}
        >
          {children}
        </motion.div>
      </Link>
    );
  }

  // For external links, use motion.a
  if (isExternalLink) {
    const hoverAnimation = enableGlow
      ? {
          scale: 1.02,
          boxShadow: `0 0 20px ${glowColor}`,
          transition: { duration: 0.3 },
        }
      : {
          scale: 1.02,
          transition: { duration: 0.3 },
        };

    return (
      <motion.a
        href={href}
        className={`${baseClasses[variant]} ${className} ${flexClasses}`}
        target="_blank"
        rel="noopener noreferrer"
        whileHover={hoverAnimation}
        whileTap={{
          scale: 0.98,
          transition: { duration: 0.15 },
        }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        style={{
          willChange: enableGlow ? 'transform, box-shadow' : 'transform',
        }}
      >
        {children}
      </motion.a>
    );
  }

  // For buttons with onClick, use motion.button
  const hoverAnimation = enableGlow
    ? {
        scale: 1.02,
        boxShadow: `0 0 20px ${glowColor}`,
        transition: { duration: 0.3 },
      }
    : {
        scale: 1.02,
        transition: { duration: 0.3 },
      };

  return (
    <motion.button
      onClick={onClick}
      className={`${baseClasses[variant]} ${className} ${flexClasses}`}
      whileHover={hoverAnimation}
      whileTap={{
        scale: 0.98,
        transition: { duration: 0.15 },
      }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      style={{
        willChange: enableGlow ? 'transform, box-shadow' : 'transform',
      }}
    >
      {children}
    </motion.button>
  );
}

interface HoverCardProps {
  children: ReactNode;
  className?: string;
  hoverScale?: number;
  hoverY?: number;
}

export function HoverCard({
  children,
  className = '',
  hoverScale = 1.01,
  hoverY = -2,
}: HoverCardProps) {
  const ref = useRef<HTMLDivElement>(null);

  return (
    <motion.div
      ref={ref}
      className={className}
      whileHover={{
        scale: hoverScale,
        y: hoverY,
        transition: { duration: 0.4 },
      }}
      initial={{
        opacity: 0,
        y: 15,
      }}
      animate={{
        opacity: 1,
        y: 0,
      }}
      transition={{ duration: 0.5 }}
      onAnimationStart={() => {
        // Prevent transform conflicts when animation starts
        preventTransformConflicts(ref.current);
      }}
      onAnimationComplete={() => {
        // Clean up animation resources after completion
        cleanupFramerMotionElement(ref.current);
      }}
      style={{
        willChange: 'transform, opacity',
        backfaceVisibility: 'hidden',
        transform: 'translateZ(0)',
      }}
    >
      {children}
    </motion.div>
  );
}

interface StaggeredCardProps {
  children: ReactNode;
  index: number;
  className?: string;
}

export function StaggeredCard({
  children,
  index,
  className = '',
}: StaggeredCardProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { opacity, y, scale, rotateX } = useStaggeredReveal(index, ref);

  return (
    <motion.div
      ref={ref}
      style={{
        opacity,
        y,
        scale,
        rotateX,
        transformPerspective: 1000,
        willChange: 'transform, opacity',
        backfaceVisibility: 'hidden',
        transform: 'translateZ(0)',
      }}
      className={className}
      onAnimationStart={() => {
        // Prevent transform conflicts when staggered animation starts
        preventTransformConflicts(ref.current);
      }}
      onAnimationComplete={() => {
        // Clean up animation resources after staggered animation completes
        cleanupFramerMotionElement(ref.current);
      }}
    >
      {children}
    </motion.div>
  );
}

interface IndependentSectionProps {
  children: ReactNode;
  className?: string;
  triggerPoint?: number;
}

export function IndependentSection({
  children,
  className = '',
  triggerPoint = 0.6,
}: IndependentSectionProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { opacity, y, scale } = useIndependentScrollTrigger(ref, triggerPoint);

  return (
    <motion.div ref={ref} style={{ opacity, y, scale }} className={className}>
      {children}
    </motion.div>
  );
}

interface HeroParallaxProps {
  children: ReactNode;
  speed?: number;
  className?: string;
}

export function HeroParallax({
  children,
  speed = 1.2,
  className = '',
}: HeroParallaxProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { y, scale } = useHeroParallax(speed, ref);

  return (
    <motion.div ref={ref} style={{ y, scale }} className={className}>
      {children}
    </motion.div>
  );
}
